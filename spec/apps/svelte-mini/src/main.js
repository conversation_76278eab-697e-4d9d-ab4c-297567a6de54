import * as Sentry from "@sentry/svelte";
import App from './App.svelte'

Sentry.init({
  dsn: "http://12345@localhost/123",
  debug: true,
  integrations: [
    Sentry.browserTracingIntegration(),
  ],
  // Set tracePropagationTargets to control for which URLs trace propagation should be enabled
  // Based on Sentry v8 documentation: https://docs.sentry.io/platforms/javascript/tracing/instrumentation/automatic-instrumentation/
  tracePropagationTargets: ["localhost", /^https?:\/\/localhost:4000/],
  tracesSampleRate: 1.0,
  environment: "test",
});

// Add some debug logging
console.log('[Sentry] Initialized with debug=true and tracePropagationTargets for localhost:4000');

const app = new App({
  target: document.getElementById('app'),
})

export default app
