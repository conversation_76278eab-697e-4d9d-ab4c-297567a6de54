import "./app.css";
import App from "./App.svelte";

import * as Sentry from "@sentry/svelte";

// Initialize the Sentry SDK here
Sentry.init({
  dsn: "http://12345@localhost/123",
  debug: true,

  // Adds request headers and IP for users, for more info visit:
  // https://docs.sentry.io/platforms/javascript/guides/svelte/configuration/options/#sendDefaultPii
  sendDefaultPii: true,

  integrations: [
    // performance
    Sentry.browserTracingIntegration(),
    // session-replay
    Sentry.replayIntegration(),
  ],

  // Enable logs to be sent to Sentry
  enableLogs: true,

  // performance
  // Set tracesSampleRate to 1.0 to capture 100%
  // of transactions for tracing.
  // We recommend adjusting this value in production
  // Learn more at
  // https://docs.sentry.io/platforms/javascript/configuration/options/#traces-sample-rate
  tracesSampleRate: 1.0,

  // Set `tracePropagationTargets` to control for which URLs trace propagation should be enabled
  tracePropagationTargets: ["localhost", /^https:\/\/localhost:4000/],
  // performance

  // session-replay
  // Capture Replay for 10% of all sessions,
  // plus 100% of sessions with an error
  // Learn more at
  // https://docs.sentry.io/platforms/javascript/session-replay/configuration/#general-integration-configuration
  replaysSessionSampleRate: 0.1,
  replaysOnErrorSampleRate: 1.0,
  // session-replay

  environment: "test",
});

// Add some debug logging
console.log('[Sentry] Initialized with debug=true and tracePropagationTargets for localhost:4000');

const app = new App({
  target: document.getElementById("app"),
});

export default app;
