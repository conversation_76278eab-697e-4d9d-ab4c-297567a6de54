<script>
  import * as Sentry from "@sentry/svelte";

  let loading = false;
  let result = "";

  async function triggerError() {
    loading = true;
    try {
      // Start a span to ensure distributed tracing headers are added
      await Sentry.startSpan(
        {
          name: "trigger-error",
          op: "navigation",
        },
        async () => {
          // Get the current trace propagation headers manually
          // In Sentry v8, use getTraceMetaData() or check if there's an active span
          const activeSpan = Sentry.getActiveSpan();
          console.log("[Sentry] Active span:", activeSpan);

          let traceHeaders = {};
          if (activeSpan) {
            // Try to get trace headers from the active span
            const traceId = activeSpan.spanContext().traceId;
            const spanId = activeSpan.spanContext().spanId;
            traceHeaders["sentry-trace"] = `${traceId}-${spanId}-1`;
            console.log(
              "[Sentry] Generated trace headers from active span:",
              traceHeaders,
            );
          } else {
            // Fallback: try to get trace headers using the propagation API
            try {
              traceHeaders = Sentry.getTraceData() || {};
              console.log(
                "[Sentry] Trace headers from getTraceData():",
                traceHeaders,
              );
            } catch (error) {
              console.log("[Sentry] Error getting trace data:", error);
            }
          }

          // Prepare headers with trace propagation
          const headers = {
            "Content-Type": "application/json",
          };

          // Add trace headers if available
          if (traceHeaders && traceHeaders["sentry-trace"]) {
            headers["sentry-trace"] = traceHeaders["sentry-trace"];
          }
          if (traceHeaders && traceHeaders["baggage"]) {
            headers["baggage"] = traceHeaders["baggage"];
          }

          console.log("[Sentry] Making request with headers:", headers);

          const response = await fetch(`${__RAILS_API_URL__}/error`, {
            method: "GET",
            headers: headers,
          });

          if (response.ok) {
            const data = await response.json();
            result = `Success: ${JSON.stringify(data)}`;
          } else {
            result = `Error: ${response.status} ${response.statusText}`;
          }
        },
      );
    } catch (error) {
      result = `Error: ${error.message}`;
    } finally {
      loading = false;
    }
  }
</script>

<main>
  <h1>Svelte Mini App</h1>
  <p>
    Click the button to trigger an error in the Rails app and test distributed
    tracing:
  </p>

  <button id="trigger-error-btn" on:click={triggerError} disabled={loading}>
    {loading ? "Loading..." : "Trigger Error"}
  </button>

  {#if result}
    <div class="result">
      <h3>Result:</h3>
      <pre>{result}</pre>
    </div>
  {/if}
</main>

<style>
  main {
    text-align: center;
    padding: 1em;
    max-width: 240px;
    margin: 0 auto;
  }

  button {
    background-color: #ff3e00;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    margin: 20px 0;
  }

  button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }

  .result {
    margin-top: 20px;
    text-align: left;
    background-color: #f5f5f5;
    padding: 10px;
    border-radius: 5px;
  }

  pre {
    white-space: pre-wrap;
    word-break: break-word;
  }
</style>
